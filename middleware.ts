import { type NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Define protected routes
  const protectedRoutes = [
    "/dashboard",
    "/profile",
    "/settings",
    "/courses",
    "/learning",
  ];

  // Define auth routes (should redirect to dashboard if already authenticated)
  const authRoutes = ["/auth/login", "/auth/register", "/auth/forgot-password"];

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some((route) =>
    pathname.startsWith(route)
  );

  // Check if the current path is an auth route
  const isAuthRoute = authRoutes.some((route) => pathname.startsWith(route));

  // Get session token from cookies
  const sessionToken = request.cookies.get("better-auth.session_token")?.value;

  // If accessing a protected route without authentication
  if (isProtectedRoute && !sessionToken) {
    const loginUrl = new URL("/auth/login", request.url);
    loginUrl.searchParams.set("redirect", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If accessing auth routes while already authenticated
  if (isAuthRoute && sessionToken) {
    const redirectUrl =
      request.nextUrl.searchParams.get("redirect") || "/dashboard";
    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  // For subdomain handling - support for 4 different user types
  const host = request.headers.get("host");
  const subdomain = host?.split(".")[0];

  // Define subdomain-specific protected routes
  const subdomainRoutes = {
    students: ["/dashboard", "/courses", "/assignments", "/grades", "/profile"],
    teachers: [
      "/dashboard",
      "/classes",
      "/students",
      "/assignments",
      "/gradebook",
      "/profile",
    ],
    admins: [
      "/dashboard",
      "/users",
      "/schools",
      "/analytics",
      "/settings",
      "/profile",
    ],
    parents: [
      "/dashboard",
      "/children",
      "/progress",
      "/communication",
      "/profile",
    ],
  };

  // Check if current subdomain requires authentication
  if (subdomain && subdomainRoutes[subdomain as keyof typeof subdomainRoutes]) {
    const allowedRoutes =
      subdomainRoutes[subdomain as keyof typeof subdomainRoutes];
    const requiresAuth = allowedRoutes.some((route) =>
      pathname.startsWith(route)
    );

    if (requiresAuth && !sessionToken) {
      const loginUrl = new URL("/auth/login", request.url);
      loginUrl.searchParams.set("redirect", pathname);
      loginUrl.searchParams.set("subdomain", subdomain);
      return NextResponse.redirect(loginUrl);
    }
  }

  // General subdomain protection for any *.learnfunda.ai subdomain
  if (host?.includes("learnfunda.ai") && !host.startsWith("www.")) {
    if (
      !sessionToken &&
      !pathname.startsWith("/auth") &&
      !pathname.startsWith("/api") &&
      !pathname.startsWith("/") // Allow homepage
    ) {
      const loginUrl = new URL("/auth/login", request.url);
      loginUrl.searchParams.set("redirect", pathname);
      loginUrl.searchParams.set("subdomain", subdomain || "");
      return NextResponse.redirect(loginUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
};
