import { type NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getUserWithRoles, SUBDOMAIN_ROLE_MAP } from "@/lib/auth/roles";
import type { SubdomainType } from "@/lib/auth/types";

// Helper function to get user session and roles
async function getUserSession(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers as Headers,
    });

    if (!session?.user) {
      return null;
    }

    const userWithRoles = await getUserWithRoles(session.user.id);
    return userWithRoles;
  } catch (error) {
    console.error("Error getting user session in middleware:", error);
    return null;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Define protected routes
  const protectedRoutes = [
    "/dashboard",
    "/profile",
    "/settings",
    "/courses",
    "/learning",
  ];

  // Define auth routes (should redirect to dashboard if already authenticated)
  const authRoutes = ["/auth/login", "/auth/register", "/auth/forgot-password"];

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some((route) =>
    pathname.startsWith(route)
  );

  // Check if the current path is an auth route
  const isAuthRoute = authRoutes.some((route) => pathname.startsWith(route));

  // Get session token from cookies
  const sessionToken = request.cookies.get("better-auth.session_token")?.value;

  // If accessing a protected route without authentication
  if (isProtectedRoute && !sessionToken) {
    const loginUrl = new URL("/auth/login", request.url);
    loginUrl.searchParams.set("redirect", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If accessing auth routes while already authenticated
  if (isAuthRoute && sessionToken) {
    const redirectUrl =
      request.nextUrl.searchParams.get("redirect") || "/dashboard";
    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  // For subdomain handling - support for 4 different user types
  const host = request.headers.get("host");
  const subdomain = host?.split(".")[0];

  // Define subdomain-specific protected routes
  const subdomainRoutes = {
    students: ["/dashboard", "/courses", "/assignments", "/grades", "/profile"],
    teachers: [
      "/dashboard",
      "/classes",
      "/students",
      "/assignments",
      "/gradebook",
      "/profile",
    ],
    admins: [
      "/dashboard",
      "/users",
      "/schools",
      "/analytics",
      "/settings",
      "/profile",
    ],
    parents: [
      "/dashboard",
      "/children",
      "/progress",
      "/communication",
      "/profile",
    ],
  };

  // Role-based subdomain access control
  if (subdomain && subdomainRoutes[subdomain as keyof typeof subdomainRoutes]) {
    const allowedRoutes =
      subdomainRoutes[subdomain as keyof typeof subdomainRoutes];
    const requiresAuth = allowedRoutes.some((route) =>
      pathname.startsWith(route)
    );

    if (requiresAuth) {
      if (!sessionToken) {
        const loginUrl = new URL("/auth/login", request.url);
        loginUrl.searchParams.set("redirect", pathname);
        loginUrl.searchParams.set("subdomain", subdomain);
        return NextResponse.redirect(loginUrl);
      }

      // Validate user has the correct role for this subdomain
      const userWithRoles = await getUserSession(request);
      if (userWithRoles) {
        const requiredRole = SUBDOMAIN_ROLE_MAP[subdomain as SubdomainType];
        const hasRequiredRole = userWithRoles.roles.some(
          (role) => role.name === requiredRole && role.isActive
        );

        if (!hasRequiredRole) {
          // User doesn't have the required role for this subdomain
          // Redirect to their appropriate subdomain or show access denied
          const userPrimaryRole = userWithRoles.roles.find(
            (role) => role.isActive
          )?.name;

          if (userPrimaryRole) {
            // Find the correct subdomain for user's role
            const correctSubdomain = Object.entries(SUBDOMAIN_ROLE_MAP).find(
              ([_, role]) => role === userPrimaryRole
            )?.[0];

            if (correctSubdomain && correctSubdomain !== subdomain) {
              // Redirect to correct subdomain
              const correctUrl = new URL(request.url);
              correctUrl.hostname = correctUrl.hostname.replace(
                subdomain,
                correctSubdomain
              );
              return NextResponse.redirect(correctUrl);
            }
          }

          // If no appropriate subdomain found, redirect to access denied
          const accessDeniedUrl = new URL("/auth/access-denied", request.url);
          accessDeniedUrl.searchParams.set("reason", "insufficient_role");
          accessDeniedUrl.searchParams.set("required", requiredRole);
          return NextResponse.redirect(accessDeniedUrl);
        }
      }
    }
  }

  // General subdomain protection for any *.learnfunda.ai subdomain
  if (host?.includes("learnfunda.ai") && !host.startsWith("www.")) {
    if (
      !sessionToken &&
      !pathname.startsWith("/auth") &&
      !pathname.startsWith("/api") &&
      !pathname.startsWith("/") // Allow homepage
    ) {
      const loginUrl = new URL("/auth/login", request.url);
      loginUrl.searchParams.set("redirect", pathname);
      loginUrl.searchParams.set("subdomain", subdomain || "");
      return NextResponse.redirect(loginUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
};
