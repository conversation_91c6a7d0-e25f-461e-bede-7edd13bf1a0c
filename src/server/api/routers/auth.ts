import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { getLoginSuccessRedirect } from "@/lib/auth/redirect";
import { assignRoleToUser } from "@/lib/auth/roles";
import { createTRPCRouter, publicProcedure } from "@/server/api/trpc";

// Validation schemas
const signUpSchema = z.object({
	name: z.string().min(2, "Name must be at least 2 characters"),
	email: z.string().email("Please enter a valid email address"),
	password: z
		.string()
		.min(8, "Password must be at least 8 characters")
		.regex(
			/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
			"Password must contain at least one uppercase letter, one lowercase letter, and one number",
		),
});

const signInSchema = z.object({
	email: z.string().email("Please enter a valid email address"),
	password: z.string().min(1, "Password is required"),
	redirectPath: z.string().optional(),
	requestedSubdomain: z.string().optional(),
});

const forgotPasswordSchema = z.object({
	email: z.string().email("Please enter a valid email address"),
});

const resetPasswordSchema = z.object({
	token: z.string().min(1, "Reset token is required"),
	password: z
		.string()
		.min(8, "Password must be at least 8 characters")
		.regex(
			/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
			"Password must contain at least one uppercase letter, one lowercase letter, and one number",
		),
});

const verifyEmailSchema = z.object({
	token: z.string().min(1, "Verification token is required"),
});

const resendVerificationSchema = z.object({
	email: z.string().email("Please enter a valid email address"),
});

export const authRouter = createTRPCRouter({
	signUp: publicProcedure.input(signUpSchema).mutation(async ({ input }) => {
		try {
			const result = await auth.api.signUpEmail({
				body: {
					email: input.email,
					password: input.password,
					name: input.name,
				},
				headers: new Headers(),
			});

			if (!result) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to create account",
				});
			}

			// Assign default STUDENT role to new user
			try {
				await assignRoleToUser(result.user.id, "STUDENT");
			} catch (roleError) {
				console.error("Failed to assign default role:", roleError);
				// Don't fail the registration if role assignment fails
				// The user can be assigned a role later by an admin
			}

			return {
				success: true,
				user: result.user,
				message:
					"Account created successfully. Please check your email for verification.",
			};
		} catch (error: unknown) {
			console.error("Sign up error:", error);

			const errorMessage =
				error instanceof Error ? error.message : String(error);
			if (errorMessage.includes("duplicate key")) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "An account with this email already exists",
				});
			}

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: errorMessage || "Failed to create account",
			});
		}
	}),

	signIn: publicProcedure.input(signInSchema).mutation(async ({ input }) => {
		try {
			const result = await auth.api.signInEmail({
				body: {
					email: input.email,
					password: input.password,
				},
				headers: new Headers(),
			});

			if (!result) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "Invalid email or password",
				});
			}

			// Get role-based redirect URL
			const redirectUrl = await getLoginSuccessRedirect(
				result.user.id,
				input.redirectPath,
				input.requestedSubdomain,
			);

			return {
				success: true,
				user: result.user,
				message: "Signed in successfully",
				redirectUrl,
			};
		} catch (error: unknown) {
			console.error("Sign in error:", error);

			const errorMessage =
				error instanceof Error ? error.message : String(error);
			if (errorMessage.includes("EMAIL_NOT_VERIFIED")) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Please verify your email address before signing in",
				});
			}

			if (errorMessage.includes("INVALID_EMAIL_OR_PASSWORD")) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "Invalid email or password",
				});
			}

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: errorMessage || "Failed to sign in",
			});
		}
	}),

	forgotPassword: publicProcedure
		.input(forgotPasswordSchema)
		.mutation(async ({ input }) => {
			try {
				const _result = await auth.api.forgetPassword({
					body: {
						email: input.email,
						redirectTo: `${process.env.NEXTAUTH_URL}/auth/reset-password`,
					},
					headers: new Headers(),
				});

				return {
					success: true,
					message: "Password reset email sent successfully",
				};
			} catch (error: unknown) {
				console.error("Forgot password error:", error);

				// Don't reveal if email exists or not for security
				return {
					success: true,
					message:
						"If an account with this email exists, you will receive a password reset link",
				};
			}
		}),

	resetPassword: publicProcedure
		.input(resetPasswordSchema)
		.mutation(async ({ input }) => {
			try {
				const result = await auth.api.resetPassword({
					body: {
						token: input.token,
						newPassword: input.password,
					},
					headers: new Headers(),
				});

				if (!result) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: "Invalid or expired reset token",
					});
				}

				return {
					success: true,
					message: "Password reset successfully",
				};
			} catch (error: unknown) {
				console.error("Reset password error:", error);

				const errorMessage =
					error instanceof Error ? error.message : String(error);
				if (errorMessage.includes("INVALID_TOKEN")) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: "This reset link has expired or is invalid",
					});
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: errorMessage || "Failed to reset password",
				});
			}
		}),

	verifyEmail: publicProcedure
		.input(verifyEmailSchema)
		.mutation(async ({ input }) => {
			try {
				const result = await auth.api.verifyEmail({
					query: {
						token: input.token,
					},
					headers: new Headers(),
				});

				if (!result) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: "Invalid or expired verification token",
					});
				}

				return {
					success: true,
					user: result.user,
					message: "Email verified successfully",
				};
			} catch (error: unknown) {
				console.error("Email verification error:", error);

				const errorMessage =
					error instanceof Error ? error.message : String(error);
				if (errorMessage.includes("INVALID_TOKEN")) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: "This verification link has expired or is invalid",
					});
				}

				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: errorMessage || "Failed to verify email",
				});
			}
		}),

	resendVerification: publicProcedure
		.input(resendVerificationSchema)
		.mutation(async ({ input }) => {
			try {
				const _result = await auth.api.sendVerificationEmail({
					body: {
						email: input.email,
						callbackURL: `${process.env.NEXTAUTH_URL}/auth/verify-email`,
					},
					headers: new Headers(),
				});

				return {
					success: true,
					message: "Verification email sent successfully",
				};
			} catch (error: unknown) {
				console.error("Resend verification error:", error);

				const errorMessage =
					error instanceof Error ? error.message : String(error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: errorMessage || "Failed to send verification email",
				});
			}
		}),

	getSession: publicProcedure.query(async () => {
		try {
			const result = await auth.api.getSession({
				headers: new Headers(),
			});

			return {
				user: result?.user || null,
				session: result?.session || null,
			};
		} catch (error: unknown) {
			console.error("Get session error:", error);
			return {
				user: null,
				session: null,
			};
		}
	}),

	signOut: publicProcedure.mutation(async () => {
		try {
			await auth.api.signOut({
				headers: new Headers(),
			});

			return {
				success: true,
				message: "Signed out successfully",
			};
		} catch (error: unknown) {
			console.error("Sign out error:", error);

			const errorMessage =
				error instanceof Error ? error.message : String(error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: errorMessage || "Failed to sign out",
			});
		}
	}),
});
