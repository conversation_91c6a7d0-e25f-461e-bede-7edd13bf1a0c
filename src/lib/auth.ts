import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";
import { Resend } from "resend";
import { env } from "@/env";

import { db } from "@/lib/db";
import * as schema from "@/lib/db/schema";

const resend = new Resend(env.RESEND_API_KEY);

export const auth = betterAuth({
	database: drizzleAdapter(db, {
		provider: "pg", // PostgreSQL
		usePlural: true, // Our tables use plural names (users, sessions, etc.)
		schema: {
			...schema,
			user: schema.users,
			session: schema.sessions,
			account: schema.accounts,
			verification: schema.verifications,
		},
	}),
	emailAndPassword: {
		enabled: true,
		requireEmailVerification: true,
		minPasswordLength: 8,
		maxPasswordLength: 128,
		sendResetPassword: async ({ user, url }) => {
			await resend.emails.send({
				from: "LearnFunda <<EMAIL>>",
				to: [user.email],
				subject: "Reset Your Password - LearnFunda",
				html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%); padding: 40px 20px; text-align: center; border-radius: 10px 10px 0 0;">
              <h1 style="color: white; margin: 0; font-size: 28px;">Reset Your Password</h1>
            </div>
            <div style="background: white; padding: 30px 20px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <h2 style="color: #1E40AF; margin-bottom: 20px;">Hello ${
								user.name || "there"
							}!</h2>
              <p style="color: #4B5563; line-height: 1.6; margin-bottom: 20px;">
                We received a request to reset your password for your LearnFunda account.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${url}" style="background: #3B82F6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                  Reset Password
                </a>
              </div>
              <p style="color: #6B7280; font-size: 14px; margin-top: 30px;">
                If you didn't request this password reset, you can safely ignore this email. This link will expire in 1 hour.
              </p>
            </div>
          </div>
        `,
			});
		},
	},
	emailVerification: {
		sendOnSignUp: true,
		autoSignInAfterVerification: true,
		sendVerificationEmail: async ({ user, url }) => {
			await resend.emails.send({
				from: "LearnFunda <<EMAIL>>",
				to: [user.email],
				subject: "Verify Your Email - LearnFunda",
				html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%); padding: 40px 20px; text-align: center; border-radius: 10px 10px 0 0;">
              <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to LearnFunda!</h1>
            </div>
            <div style="background: white; padding: 30px 20px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <h2 style="color: #1E40AF; margin-bottom: 20px;">Hello ${
								user.name || "there"
							}!</h2>
              <p style="color: #4B5563; line-height: 1.6; margin-bottom: 20px;">
                Thank you for signing up for LearnFunda! Please verify your email address to complete your account setup.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${url}" style="background: #3B82F6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                  Verify Email Address
                </a>
              </div>
              <p style="color: #6B7280; font-size: 14px; margin-top: 30px;">
                If you didn't create this account, you can safely ignore this email.
              </p>
            </div>
          </div>
        `,
			});
		},
	},
	session: {
		expiresIn: 60 * 60 * 24 * 7, // 7 days
		updateAge: 60 * 60 * 24, // 1 day
		cookieCache: {
			enabled: true,
			maxAge: 60 * 5, // 5 minutes
		},
	},

	advanced: {
		crossSubDomainCookies: {
			enabled: true,
			domain: ".learnfunda.ai", // For cross-subdomain support
		},
	},
	secret: env.BETTER_AUTH_SECRET,
	baseURL: env.BETTER_AUTH_URL,
	trustedOrigins: [
		"http://localhost:3000",
		"https://learnfunda.ai",
		"https://*.learnfunda.ai",
		// Specific subdomains for the 4 different user types
		"https://students.learnfunda.ai",
		"https://teachers.learnfunda.ai",
		"https://admins.learnfunda.ai",
		"https://parents.learnfunda.ai",
	],
	rateLimit: {
		enabled: true,
		window: 60, // 1 minute
		max: 100, // 100 requests per minute per IP
	},
	csrf: {
		enabled: true,
		sameSite: "lax",
		secure: env.NODE_ENV === "production",
	},
	plugins: [nextCookies()], // Must be last plugin in the array
});

export type Session = typeof auth.$Infer.Session;
export type AuthUser = typeof auth.$Infer.Session.user;
