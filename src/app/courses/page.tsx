"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import StudentLayout from "@/components/layouts/StudentLayout"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useSession } from "@/lib/auth-client"
import { api } from "@/trpc/react"

export default function CoursesPage() {
	const { data: session } = useSession()
	const router = useRouter()
	const [selectedCourse, setSelectedCourse] = useState<string | null>(null)

	const { data: coursesData, isLoading } = api.courses.getMyCourses.useQuery(
		undefined,
		{
			enabled: !!session?.user,
		},
	)

	const { data: courseDetails } = api.courses.getCourseDetails.useQuery(
		{ subjectId: selectedCourse ?? "" },
		{ enabled: !!selectedCourse },
	)

	if (isLoading) {
		return (
			<StudentLayout>
				<div className="flex items-center justify-center min-h-96">
					<div className="text-center">
						<Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
						<p className="text-gray-600">Loading your courses...</p>
					</div>
				</div>
			</StudentLayout>
		)
	}

	const handleCourseClick = (courseId: string) => {
		setSelectedCourse(courseId)
	}

	const handleTopicClick = (topicId: string) => {
		router.push(`/courses/topics/${topicId}`)
	}

	return (
		<StudentLayout>
			<div className="p-6 space-y-6">
				{/* Header */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900">My Courses</h1>
					<p className="text-gray-600 mt-2">
						{coursesData?.userProfile && (
							<>
								{coursesData.userProfile.standard} Standard •{" "}
								{coursesData.userProfile.educationalBoard}
							</>
						)}
					</p>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
					{/* Courses List */}
					<div className="lg:col-span-2">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							{coursesData?.courses.map((course) => (
								<Card
									key={course.id}
									className={`cursor-pointer transition-all hover:shadow-lg ${selectedCourse === course.id ? "ring-2 ring-blue-500" : ""
										}`}
									onClick={() => handleCourseClick(course.id)}
								>
									<CardHeader>
										<div className="flex items-center justify-between">
											<div
												className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg"
												style={{
													backgroundColor: course.colorCode || "#3B82F6",
												}}
											>
												{course.iconUrl ? (
													<img
														src={course.iconUrl}
														alt={course.name}
														className="w-8 h-8"
													/>
												) : (
													course.name.charAt(0)
												)}
											</div>
											<Badge variant="secondary">
												{Math.floor(Math.random() * 50 + 30)}% Complete
											</Badge>
										</div>
										<CardTitle className="text-lg">{course.name}</CardTitle>
										<CardDescription className="text-sm">
											{course.description}
										</CardDescription>
									</CardHeader>
									<CardContent>
										<div className="space-y-3">
											<Progress
												value={Math.floor(Math.random() * 50 + 30)}
												className="h-2"
											/>

											<div className="flex items-center justify-between text-sm text-gray-600">
												<div className="flex items-center gap-1">
													<BookOpen className="h-4 w-4" />
													<span>
														{Math.floor(Math.random() * 10 + 5)} Topics
													</span>
												</div>
												<div className="flex items-center gap-1">
													<Clock className="h-4 w-4" />
													<span>{Math.floor(Math.random() * 20 + 10)}h</span>
												</div>
											</div>

											<Button className="w-full" size="sm">
												Continue Learning
											</Button>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					</div>

					{/* Course Details Sidebar */}
					<div className="lg:col-span-1">
						{selectedCourse && courseDetails ? (
							<Card className="sticky top-6">
								<CardHeader>
									<CardTitle className="flex items-center gap-2">
										<BookOpen className="h-5 w-5" />
										{courseDetails.subject.name}
									</CardTitle>
									<CardDescription>
										{courseDetails.subject.description}
									</CardDescription>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="space-y-2">
										<h4 className="font-medium text-gray-900">Course Topics</h4>
										<div className="space-y-2 max-h-96 overflow-y-auto">
											{courseDetails.topics.map((topic, index) => (
												<div
													key={topic.id}
													className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
													onClick={() => handleTopicClick(topic.id)}
												>
													<div className="flex-1">
														<div className="flex items-center gap-2">
															<span className="text-sm font-medium text-gray-500">
																{index + 1}.
															</span>
															<span className="text-sm font-medium text-gray-900">
																{topic.name}
															</span>
														</div>
														<div className="flex items-center gap-4 mt-1">
															<Badge
																variant={
																	topic.difficultyLevel === "BEGINNER"
																		? "secondary"
																		: topic.difficultyLevel === "INTERMEDIATE"
																			? "default"
																			: "destructive"
																}
																className="text-xs"
															>
																{topic.difficultyLevel}
															</Badge>
															{topic.estimatedDuration && (
																<div className="flex items-center gap-1 text-xs text-gray-500">
																	<Clock className="h-3 w-3" />
																	<span>{topic.estimatedDuration}min</span>
																</div>
															)}
														</div>
													</div>
													<ChevronRight className="h-4 w-4 text-gray-400" />
												</div>
											))}
										</div>
									</div>

									<div className="pt-4 border-t">
										<Button
											className="w-full"
											onClick={() => {
												if (courseDetails.topics.length > 0) {
													handleTopicClick(courseDetails.topics[0].id)
												}
											}}
										>
											Start Course
										</Button>
									</div>
								</CardContent>
							</Card>
						) : (
							<Card className="sticky top-6">
								<CardContent className="p-6 text-center">
									<BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
									<h3 className="font-medium text-gray-900 mb-2">
										Select a Course
									</h3>
									<p className="text-sm text-gray-600">
										Click on a course to view its topics and start learning.
									</p>
								</CardContent>
							</Card>
						)}
					</div>
				</div>
			</div>
		</StudentLayout>
	)
}
