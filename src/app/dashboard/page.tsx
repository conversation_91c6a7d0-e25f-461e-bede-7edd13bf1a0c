"use client";

import { Loader2 } from "lucide-react";
import StudentDashboard from "@/components/dashboard/StudentDashboard";
import StudentLayout from "@/components/layouts/StudentLayout";
import { useSession } from "@/lib/auth-client";
import { api } from "@/trpc/react";

export default function DashboardPage() {
	const { data: session, isPending } = useSession();
	const { data: userRoles } = api.roles.getMyRoles.useQuery(undefined, {
		enabled: !!session?.user,
	});

	if (isPending) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50">
				<div className="text-center">
					<Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
					<p className="text-gray-600">Loading...</p>
				</div>
			</div>
		);
	}

	// Check if user has STUDENT role
	const isStudent = userRoles?.roles?.some((role) => role.name === "STUDENT");

	if (isStudent) {
		return (
			<StudentLayout>
				<StudentDashboard />
			</StudentLayout>
		);
	}

	// Fallback for other roles or no roles
	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50">
			<div className="text-center">
				<h1 className="text-2xl font-bold text-gray-900 mb-4">
					Dashboard Not Available
				</h1>
				<p className="text-gray-600">
					This dashboard is only available for students. Please contact support
					if you believe this is an error.
				</p>
			</div>
		</div>
	);
}
