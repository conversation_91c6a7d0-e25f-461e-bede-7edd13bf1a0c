"use client";

import {
	<PERSON>ertCircle,
	CheckCircle,
	Loader2,
	LogOut,
	Shield,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useSession } from "@/lib/auth-client";

export default function DashboardPage() {
	const { data: session, isPending } = useSession();
	const router = useRouter();

	if (isPending) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50">
				<div className="text-center">
					<Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
					<p className="text-gray-600">Loading...</p>
				</div>
			</div>
		);
	}

	const handleSignOut = async () => {
		try {
			await fetch("/api/auth/sign-out", { method: "POST" });
			router.push("/");
		} catch (error) {
			console.error("Sign out error:", error);
		}
	};

	return (
		<div className="min-h-screen bg-gray-50">
			{/* Header */}
			<header className="bg-white shadow-sm border-b">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center h-16">
						<div className="flex items-center">
							<div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
								<span className="text-white font-bold text-sm">L</span>
							</div>
							<span className="ml-3 text-xl font-semibold text-gray-800">
								LearnFunda
							</span>
						</div>
						<Button
							variant="outline"
							onClick={handleSignOut}
							className="flex items-center gap-2"
						>
							<LogOut className="h-4 w-4" />
							Sign Out
						</Button>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900">
						Welcome to your Dashboard!
					</h1>
					<p className="text-gray-600 mt-2">
						You have successfully authenticated with LearnFunda.
					</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
					{/* Authentication Status */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Shield className="h-5 w-5 text-green-600" />
								Authentication Status
							</CardTitle>
							<CardDescription>Current session information</CardDescription>
						</CardHeader>
						<CardContent className="space-y-3">
							<div className="flex items-center gap-2">
								<CheckCircle className="h-4 w-4 text-green-600" />
								<span className="text-sm">Authenticated</span>
							</div>
							{session && (
								<>
									<div>
										<Label className="text-sm font-medium text-gray-500">
											Email
										</Label>
										<p className="text-gray-900">{session.user.email}</p>
									</div>
									<div>
										<Label className="text-sm font-medium text-gray-500">
											Name
										</Label>
										<p className="text-gray-900">
											{session.user.name || "Not provided"}
										</p>
									</div>
									<div>
										<Label className="text-sm font-medium text-gray-500">
											Email Verified
										</Label>
										<div className="flex items-center gap-2">
											{session.user.emailVerified ? (
												<>
													<CheckCircle className="h-4 w-4 text-green-600" />
													<span className="text-green-600 text-sm">
														Verified
													</span>
												</>
											) : (
												<>
													<AlertCircle className="h-4 w-4 text-orange-600" />
													<span className="text-orange-600 text-sm">
														Not verified
													</span>
												</>
											)}
										</div>
									</div>
								</>
							)}
						</CardContent>
					</Card>

					{/* Middleware Protection */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Shield className="h-5 w-5 text-blue-600" />
								Route Protection
							</CardTitle>
							<CardDescription>Middleware security features</CardDescription>
						</CardHeader>
						<CardContent className="space-y-3">
							<div className="flex items-center gap-2">
								<CheckCircle className="h-4 w-4 text-green-600" />
								<span className="text-sm">Protected route accessed</span>
							</div>
							<div className="flex items-center gap-2">
								<CheckCircle className="h-4 w-4 text-green-600" />
								<span className="text-sm">Session validation passed</span>
							</div>
							<div className="flex items-center gap-2">
								<CheckCircle className="h-4 w-4 text-green-600" />
								<span className="text-sm">Middleware security active</span>
							</div>
						</CardContent>
					</Card>

					{/* Quick Actions */}
					<Card>
						<CardHeader>
							<CardTitle>Quick Actions</CardTitle>
							<CardDescription>Test protected routes</CardDescription>
						</CardHeader>
						<CardContent className="space-y-3">
							<Button
								variant="outline"
								className="w-full justify-start"
								onClick={() => router.push("/profile")}
							>
								View Profile (Protected)
							</Button>
							<Button
								variant="outline"
								className="w-full justify-start"
								onClick={() => router.push("/settings")}
							>
								Settings (Protected)
							</Button>
							<Button
								variant="outline"
								className="w-full justify-start"
								onClick={() => router.push("/courses")}
							>
								Courses (Protected)
							</Button>
						</CardContent>
					</Card>
				</div>

				{/* Success Message */}
				<div className="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg">
					<h3 className="text-lg font-semibold text-green-800 mb-2">
						🎉 Authentication & Middleware Test Successful!
					</h3>
					<p className="text-green-700">
						You have successfully completed the authentication flow with
						middleware protection. This dashboard demonstrates that:
					</p>
					<ul className="list-disc list-inside mt-3 text-green-700 space-y-1">
						<li>User registration and email verification work correctly</li>
						<li>Login flow with session persistence is functional</li>
						<li>Protected routes are properly secured by middleware</li>
						<li>Session data is accessible and properly formatted</li>
						<li>Middleware redirects unauthenticated users to login</li>
						<li>Cross-subdomain authentication is configured</li>
					</ul>
				</div>
			</main>
		</div>
	);
}
